<?php

namespace App\Http\Controllers\Api\V1;

use App\Data\Api\V1\MessageData;
use App\Enums\MessageStatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\ContactRequest;
use App\Http\Resources\Api\V1\MessageResource;
use App\Mail\ContactMail;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Illuminate\Validation\ValidationException;

class ContactController extends Controller
{
    public function store(ContactRequest $request): JsonResource
    {
        // Turnstile validation
        $response = Http::asForm()->post('https://challenges.cloudflare.com/turnstile/v0/siteverify', [
            'secret'   => config('services.turnstile.secret'),
            'response' => $request->turnstile_response,
            'remoteip' => $request->ip(),
        ]);

        if (! $response->ok() || ! $response->json('success')) {
            throw ValidationException::withMessages([
                'turnstile_response' => ['The Turnstile verification failed. Please try again.'],
            ]);
        }

        // Send contact email
        Mail::to(config('mail.contact'))
            ->send(new ContactMail(
                $request->email,
                $request->subject,
                $request->content
            ));

        return new MessageResource(MessageData::from([
            'status' => MessageStatusEnum::SUCCESS,
            'message' => 'Your message has been sent successfully!',
        ]));
    }
}
